/**
 * 统一 HTTP 客户端配置
 * 包含超时、重试、错误处理等功能
 */

import axios, {
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  AxiosError,
} from "axios";
import { apiBaseUrl, isDebugEnabled } from "./index";

// 重试配置
interface RetryConfig {
  retries: number;
  retryDelay: number;
  retryCondition?: (error: AxiosError) => boolean;
  exponentialBackoff: boolean;
  maxRetryDelay: number;
}

// 默认重试配置
const DEFAULT_RETRY_CONFIG: RetryConfig = {
  retries: 3,
  retryDelay: 1000, // 1秒
  exponentialBackoff: true,
  maxRetryDelay: 10000, // 10秒
  retryCondition: (error: AxiosError) => {
    // 只对网络错误、5xx错误、429错误重试
    if (!error.response) return true; // 网络错误
    const status = error.response.status;
    return status >= 500 || status === 429;
  },
};

// 幂等操作的HTTP方法（安全重试）
const IDEMPOTENT_METHODS = ["GET", "HEAD", "OPTIONS", "PUT", "DELETE"];

/**
 * 计算重试延迟时间
 */
function calculateRetryDelay(attempt: number, config: RetryConfig): number {
  if (!config.exponentialBackoff) {
    return config.retryDelay;
  }

  // 指数退避：delay * 2^attempt + 随机抖动
  const exponentialDelay = config.retryDelay * Math.pow(2, attempt);
  const jitter = Math.random() * 0.1 * exponentialDelay; // 10% 随机抖动
  const totalDelay = exponentialDelay + jitter;

  return Math.min(totalDelay, config.maxRetryDelay);
}

/**
 * 检查操作是否幂等
 */
function isIdempotent(config: AxiosRequestConfig): boolean {
  const method = (config.method || "GET").toUpperCase();
  return IDEMPOTENT_METHODS.includes(method);
}

/**
 * 重试拦截器
 */
function retryInterceptor(
  error: AxiosError,
  retryConfig: RetryConfig
): Promise<AxiosResponse> {
  const config = error.config as AxiosRequestConfig & { __retryCount?: number };

  // 初始化重试计数
  if (!config.__retryCount) {
    config.__retryCount = 0;
  }

  // 检查是否应该重试
  const shouldRetry =
    config.__retryCount < retryConfig.retries &&
    (retryConfig.retryCondition ? retryConfig.retryCondition(error) : true) &&
    (isIdempotent(config) || config.method?.toUpperCase() === "POST"); // POST 需要特殊处理

  if (!shouldRetry) {
    return Promise.reject(error);
  }

  config.__retryCount++;
  const delay = calculateRetryDelay(config.__retryCount - 1, retryConfig);

  if (isDebugEnabled) {
    console.log(
      `Retrying request (attempt ${config.__retryCount}/${retryConfig.retries}) after ${delay}ms:`,
      {
        url: config.url,
        method: config.method,
        error: error.message,
      }
    );
  }

  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(axios.request(config));
    }, delay);
  });
}

/**
 * 创建 HTTP 客户端实例
 */
function createHttpClient(
  baseURL?: string,
  timeout: number = 15000,
  retryConfig: Partial<RetryConfig> = {}
): AxiosInstance {
  const finalRetryConfig = { ...DEFAULT_RETRY_CONFIG, ...retryConfig };

  const instance = axios.create({
    baseURL,
    timeout,
    headers: {
      "Content-Type": "application/json",
    },
  });

  // 请求拦截器
  instance.interceptors.request.use(
    (config) => {
      if (isDebugEnabled) {
        console.log(
          `HTTP Request: ${config.method?.toUpperCase()} ${config.url}`,
          {
            params: config.params,
            data: config.data,
          }
        );
      }
      return config;
    },
    (error) => {
      console.error("HTTP Request Error:", error);
      return Promise.reject(error);
    }
  );

  // 响应拦截器
  instance.interceptors.response.use(
    (response) => {
      if (isDebugEnabled) {
        console.log(
          `HTTP Response: ${response.config.method?.toUpperCase()} ${
            response.config.url
          }`,
          {
            status: response.status,
            data: response.data,
          }
        );
      }
      return response;
    },
    async (error: AxiosError) => {
      // 记录错误
      console.error("HTTP Response Error:", {
        url: error.config?.url,
        method: error.config?.method,
        status: error.response?.status,
        message: error.message,
        data: error.response?.data,
      });

      // 尝试重试
      try {
        return await retryInterceptor(error, finalRetryConfig);
      } catch (retryError) {
        return Promise.reject(retryError);
      }
    }
  );

  return instance;
}

/**
 * 预配置的客户端实例
 */

// 内部 API 客户端（后端接口）
export const internalApiClient = createHttpClient(apiBaseUrl, 30000, {
  retries: 3,
  retryDelay: 1000,
  exponentialBackoff: true,
});

// 第三方 API 客户端（外部服务）
export const externalApiClient = createHttpClient(undefined, 15000, {
  retries: 2,
  retryDelay: 2000,
  exponentialBackoff: true,
  retryCondition: (error: AxiosError) => {
    // 第三方API更严格的重试条件
    if (!error.response) return true; // 网络错误
    const status = error.response.status;
    return status >= 500; // 只对服务器错误重试，不重试429
  },
});

// 上传专用客户端（更长超时，无重试）
export const uploadApiClient = createHttpClient(apiBaseUrl, 300000, {
  retries: 1, // 上传只重试一次
  retryDelay: 5000,
  exponentialBackoff: false,
  retryCondition: (error: AxiosError) => {
    // 上传只对网络错误重试
    return !error.response;
  },
});

// 快速操作客户端（短超时，快速失败）
export const quickApiClient = createHttpClient(apiBaseUrl, 5000, {
  retries: 1,
  retryDelay: 500,
  exponentialBackoff: false,
});

/**
 * 创建自定义客户端的工厂函数
 */
export function createCustomHttpClient(options: {
  baseURL?: string;
  timeout?: number;
  retryConfig?: Partial<RetryConfig>;
  headers?: Record<string, string>;
}): AxiosInstance {
  const client = createHttpClient(
    options.baseURL,
    options.timeout,
    options.retryConfig
  );

  if (options.headers) {
    Object.entries(options.headers).forEach(([key, value]) => {
      client.defaults.headers.common[key] = value;
    });
  }

  return client;
}

/**
 * 错误类型定义
 */
export interface HttpError {
  message: string;
  status?: number;
  code?: string;
  data?: any;
  isNetworkError: boolean;
  isTimeout: boolean;
  isServerError: boolean;
  isClientError: boolean;
}

/**
 * 错误处理工具函数
 */
export function handleHttpError(error: AxiosError): HttpError {
  const isNetworkError = !error.response;
  const isTimeout =
    error.code === "ECONNABORTED" || error.message.includes("timeout");
  const status = error.response?.status;
  const isServerError = status ? status >= 500 : false;
  const isClientError = status ? status >= 400 && status < 500 : false;

  return {
    message: error.message,
    status,
    code: error.code,
    data: error.response?.data,
    isNetworkError,
    isTimeout,
    isServerError,
    isClientError,
  };
}

/**
 * 带重试的请求工具函数
 */
export async function retryableRequest<T>(
  requestFn: () => Promise<AxiosResponse<T>>,
  retryConfig?: Partial<RetryConfig>
): Promise<T> {
  const config = { ...DEFAULT_RETRY_CONFIG, ...retryConfig };
  let lastError: AxiosError;

  for (let attempt = 0; attempt <= config.retries; attempt++) {
    try {
      const response = await requestFn();
      return response.data;
    } catch (error) {
      lastError = error as AxiosError;

      if (attempt === config.retries) {
        break; // 最后一次尝试失败
      }

      if (config.retryCondition && !config.retryCondition(lastError)) {
        break; // 不满足重试条件
      }

      const delay = calculateRetryDelay(attempt, config);
      await new Promise((resolve) => setTimeout(resolve, delay));
    }
  }

  throw lastError!;
}

export default {
  internalApiClient,
  externalApiClient,
  uploadApiClient,
  quickApiClient,
  createCustomHttpClient,
  handleHttpError,
  retryableRequest,
};
