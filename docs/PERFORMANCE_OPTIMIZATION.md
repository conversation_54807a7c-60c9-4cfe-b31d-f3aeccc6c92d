# 性能优化指南

## 概述

本文档详细介绍了 Fabric 视频编辑器的性能优化策略、实施计划和最佳实践。项目正在进行全面的性能优化，旨在提升用户体验和系统可扩展性。

## 🎯 优化目标

### 性能指标目标

- **时间轴渲染**: 50+ 元素项目 < 2 秒
- **UI 响应性**: 用户交互 < 100ms
- **内存使用**: 典型项目 < 500MB
- **视频处理**: 处理时间减少 30%
- **包大小**: 初始加载 < 2MB
- **首屏加载**: < 3 秒

### 用户体验目标

- 流畅的拖拽操作
- 实时的预览更新
- 快速的元素添加和删除
- 稳定的长时间编辑会话
- 优化的移动设备体验

## 🚀 优化重点领域

### 1. 前端性能优化

#### 1.1 Store 架构优化

**当前问题**:

- 单体 Store 类过于庞大
- 状态更新触发不必要的重渲染
- 缺乏细粒度的状态管理

**优化方案**:

```typescript
// 分解为专注的领域存储
interface OptimizedStoreArchitecture {
  canvasStore: CanvasStore; // 画布相关状态
  elementStore: ElementStore; // 元素管理
  timelineStore: TimelineStore; // 时间轴状态
  animationStore: AnimationStore; // 动画管理
  uiStore: UIStore; // UI 状态
}

// 使用 MobX 的 computed 和 reaction 优化
class ElementStore {
  @computed
  get visibleElements() {
    return this.elements.filter(
      (el) =>
        el.timeFrame.start <= this.currentTime &&
        el.timeFrame.end >= this.currentTime
    );
  }

  @action.bound
  updateElement(id: string, updates: Partial<Element>) {
    const element = this.elements.find((el) => el.id === id);
    if (element) {
      Object.assign(element, updates);
    }
  }
}
```

#### 1.2 组件性能优化

**React 组件优化**:

```typescript
// 使用 React.memo 和 observer
const TimelineElement = React.memo(
  observer(({ element }: { element: Element }) => {
    // 使用 useMemo 缓存计算结果
    const elementStyle = useMemo(
      () => ({
        left: `${element.timeFrame.start / 10}px`,
        width: `${(element.timeFrame.end - element.timeFrame.start) / 10}px`,
      }),
      [element.timeFrame.start, element.timeFrame.end]
    );

    // 使用 useCallback 缓存事件处理器
    const handleClick = useCallback(() => {
      store.selectElement(element.id);
    }, [element.id]);

    return (
      <div style={elementStyle} onClick={handleClick}>
        {element.name}
      </div>
    );
  })
);

// 虚拟化长列表
const VirtualizedTimeline = () => {
  return (
    <FixedSizeList
      height={400}
      itemCount={elements.length}
      itemSize={50}
      itemData={elements}
    >
      {TimelineElement}
    </FixedSizeList>
  );
};
```

#### 1.3 时间轴虚拟化

**虚拟滚动实现**:

```typescript
interface VirtualTimelineProps {
  elements: Element[];
  containerHeight: number;
  itemHeight: number;
}

const VirtualTimeline: React.FC<VirtualTimelineProps> = ({
  elements,
  containerHeight,
  itemHeight,
}) => {
  const [scrollTop, setScrollTop] = useState(0);

  const visibleRange = useMemo(() => {
    const start = Math.floor(scrollTop / itemHeight);
    const end = Math.min(
      start + Math.ceil(containerHeight / itemHeight) + 1,
      elements.length
    );
    return { start, end };
  }, [scrollTop, itemHeight, containerHeight, elements.length]);

  const visibleElements = useMemo(
    () => elements.slice(visibleRange.start, visibleRange.end),
    [elements, visibleRange]
  );

  return (
    <div
      style={{ height: containerHeight, overflow: "auto" }}
      onScroll={(e) => setScrollTop(e.currentTarget.scrollTop)}
    >
      <div style={{ height: elements.length * itemHeight }}>
        <div
          style={{
            transform: `translateY(${visibleRange.start * itemHeight}px)`,
          }}
        >
          {visibleElements.map((element, index) => (
            <TimelineElement
              key={element.id}
              element={element}
              index={visibleRange.start + index}
            />
          ))}
        </div>
      </div>
    </div>
  );
};
```

#### 1.4 媒体处理优化

**图片加载性能优化**:

```typescript
// 图片加载优化常量配置
const THUMBNAIL_WIDTH = 400; // 缩略图宽度
const IMAGE_QUALITY = 80; // 图片质量
const SKELETON_COUNT = 6; // 加载时显示的骨架屏数量
const MAX_CONCURRENT_LOADS = 3; // 最大并发加载数

// 简化的图片渲染组件
const renderImageItem = (image: ImageFile, index: number) => (
  <ImageListItem
    key={`${image.id}-${index}`}
    draggable
    onDragStart={(e) => handleDragStart(e, image, index)}
    onClick={() => handleAddImage(image.src.original, image.id)}
    sx={{
      cursor: "pointer",
      transition: "all 0.3s ease",
      "&:hover": {
        transform: "translateY(-4px)",
        boxShadow: "0 4px 12px rgba(0,0,0,0.15)",
      },
    }}
  >
    <img
      src={`${image.src.medium}?auto=compress&w=${THUMBNAIL_WIDTH}&q=${IMAGE_QUALITY}`}
      alt={image.photographer || "Unknown"}
      loading="lazy" // 使用原生懒加载
      style={{
        opacity: loadingImages[image.id.toString()] ? 0.5 : 1,
        pointerEvents: "none",
      }}
      srcSet={`
        ${
          image.src.medium
        }?auto=compress&w=${THUMBNAIL_WIDTH}&q=${IMAGE_QUALITY} 1x,
        ${image.src.large}?auto=compress&w=${
        THUMBNAIL_WIDTH * 2
      }&q=${IMAGE_QUALITY} 2x
      `} // 响应式图片支持
    />
    {loadingImages[image.id.toString()] && <CircularProgress size={24} />}
  </ImageListItem>
);
```

**缩略图生成优化**:

```typescript
class ThumbnailCache {
  private cache = new Map<string, string>();
  private maxCacheSize = 100;

  async getThumbnail(src: string, options: ThumbnailOptions): Promise<string> {
    const cacheKey = `${src}-${JSON.stringify(options)}`;

    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)!;
    }

    const thumbnail = await this.generateThumbnail(src, options);

    // 限制缓存大小
    if (this.cache.size >= this.maxCacheSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }

    this.cache.set(cacheKey, thumbnail);
    return thumbnail;
  }

  private async generateThumbnail(
    src: string,
    options: ThumbnailOptions
  ): Promise<string> {
    return new Promise((resolve, reject) => {
      const video = document.createElement("video");
      video.crossOrigin = "anonymous";

      const canvas = document.createElement("canvas");
      const ctx = canvas.getContext("2d")!;

      // 使用较小的画布尺寸提高性能
      canvas.width = Math.min(options.width || 160, 160);
      canvas.height = Math.min(options.height || 90, 90);

      video.onloadeddata = () => {
        video.currentTime = options.time || 0;
      };

      video.onseeked = () => {
        ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
        resolve(canvas.toDataURL("image/jpeg", 0.6));
      };

      video.onerror = reject;
      video.src = src;
    });
  }
}
```

### 2. 后端性能优化

#### 2.1 FFmpeg 命令优化

**硬件加速检测**:

```typescript
class HardwareAccelerationDetector {
  private static supportedEncoders: string[] = [];

  static async detectAvailableEncoders(): Promise<string[]> {
    if (this.supportedEncoders.length > 0) {
      return this.supportedEncoders;
    }

    try {
      const { stdout } = await execAsync("ffmpeg -encoders");
      const encoders = this.parseEncoders(stdout);

      // 检测硬件加速编码器
      const hwEncoders = [
        "h264_nvenc", // NVIDIA
        "h264_qsv", // Intel Quick Sync
        "h264_videotoolbox", // macOS
        "h264_amf", // AMD
      ];

      this.supportedEncoders = hwEncoders.filter((encoder) =>
        encoders.includes(encoder)
      );

      return this.supportedEncoders;
    } catch (error) {
      console.warn("Failed to detect hardware encoders:", error);
      return ["libx264"]; // 回退到软件编码
    }
  }

  private static parseEncoders(output: string): string[] {
    return output
      .split("\n")
      .filter((line) => line.includes("V....."))
      .map((line) => line.split(" ").pop()!)
      .filter(Boolean);
  }
}

// 优化的 FFmpeg 命令生成
class OptimizedFFmpegGenerator {
  async generateCommand(canvasState: CanvasState): Promise<string[]> {
    const encoders =
      await HardwareAccelerationDetector.detectAvailableEncoders();
    const encoder = encoders[0] || "libx264";

    const baseCommand = [
      "ffmpeg",
      "-y", // 覆盖输出文件
      "-threads",
      "0", // 使用所有可用线程
    ];

    // 根据编码器调整参数
    if (encoder.includes("nvenc")) {
      baseCommand.push("-hwaccel", "cuda", "-hwaccel_output_format", "cuda");
    } else if (encoder.includes("qsv")) {
      baseCommand.push("-hwaccel", "qsv", "-hwaccel_output_format", "qsv");
    }

    // 添加输入和输出参数
    baseCommand.push(
      ...this.generateInputs(canvasState),
      "-c:v",
      encoder,
      "-preset",
      "fast", // 平衡质量和速度
      "-crf",
      "23", // 合理的质量设置
      ...this.generateFilters(canvasState),
      this.getOutputPath()
    );

    return baseCommand;
  }
}
```

#### 2.2 资源管理优化

**连接池和内存管理**:

```typescript
class ResourceManager {
  private static instance: ResourceManager;
  private taskQueue: TaskQueue;
  private memoryMonitor: MemoryMonitor;

  constructor() {
    this.taskQueue = new TaskQueue({
      maxConcurrent: process.env.MAX_CONCURRENT_TASKS || 3,
      timeout: 300000, // 5 分钟超时
    });

    this.memoryMonitor = new MemoryMonitor({
      maxMemoryUsage: 0.8, // 80% 内存使用率
      checkInterval: 10000, // 10 秒检查一次
    });
  }

  async processVideo(canvasState: CanvasState): Promise<string> {
    return this.taskQueue.add(async () => {
      // 检查内存使用情况
      if (this.memoryMonitor.isMemoryHigh()) {
        await this.cleanupResources();
      }

      const processor = new VideoProcessor();
      try {
        return await processor.process(canvasState);
      } finally {
        processor.cleanup();
      }
    });
  }

  private async cleanupResources(): Promise<void> {
    // 清理临时文件
    await this.cleanupTempFiles();

    // 强制垃圾回收
    if (global.gc) {
      global.gc();
    }
  }
}

class MemoryMonitor {
  private options: MemoryMonitorOptions;
  private interval: NodeJS.Timeout;

  constructor(options: MemoryMonitorOptions) {
    this.options = options;
    this.startMonitoring();
  }

  isMemoryHigh(): boolean {
    const usage = process.memoryUsage();
    const totalMemory = require("os").totalmem();
    const memoryUsageRatio = usage.heapUsed / totalMemory;

    return memoryUsageRatio > this.options.maxMemoryUsage;
  }

  private startMonitoring(): void {
    this.interval = setInterval(() => {
      const usage = process.memoryUsage();
      console.log("Memory usage:", {
        heapUsed: Math.round(usage.heapUsed / 1024 / 1024) + "MB",
        heapTotal: Math.round(usage.heapTotal / 1024 / 1024) + "MB",
        external: Math.round(usage.external / 1024 / 1024) + "MB",
      });
    }, this.options.checkInterval);
  }
}
```

### 3. 数据库和缓存优化

#### 3.1 缓存策略

**多层缓存架构**:

```typescript
interface CacheLayer {
  get(key: string): Promise<any>;
  set(key: string, value: any, ttl?: number): Promise<void>;
  delete(key: string): Promise<void>;
}

class MultiLayerCache {
  private memoryCache: Map<string, CacheItem> = new Map();
  private redisCache?: RedisCache;
  private fileCache: FileCache;

  constructor() {
    this.fileCache = new FileCache("./cache");

    // 如果有 Redis 配置，启用 Redis 缓存
    if (process.env.REDIS_URL) {
      this.redisCache = new RedisCache(process.env.REDIS_URL);
    }
  }

  async get(key: string): Promise<any> {
    // L1: 内存缓存
    if (this.memoryCache.has(key)) {
      const item = this.memoryCache.get(key)!;
      if (!this.isExpired(item)) {
        return item.value;
      }
      this.memoryCache.delete(key);
    }

    // L2: Redis 缓存
    if (this.redisCache) {
      const value = await this.redisCache.get(key);
      if (value !== null) {
        this.memoryCache.set(key, {
          value,
          timestamp: Date.now(),
          ttl: 300000, // 5 分钟
        });
        return value;
      }
    }

    // L3: 文件缓存
    const value = await this.fileCache.get(key);
    if (value !== null) {
      // 回填到上层缓存
      if (this.redisCache) {
        await this.redisCache.set(key, value, 3600); // 1 小时
      }
      this.memoryCache.set(key, {
        value,
        timestamp: Date.now(),
        ttl: 300000,
      });
    }

    return value;
  }

  async set(key: string, value: any, ttl: number = 3600): Promise<void> {
    // 写入所有层级
    this.memoryCache.set(key, {
      value,
      timestamp: Date.now(),
      ttl: Math.min(ttl * 1000, 300000), // 内存缓存最多 5 分钟
    });

    if (this.redisCache) {
      await this.redisCache.set(key, value, ttl);
    }

    await this.fileCache.set(key, value);
  }
}
```

## 📊 性能监控

### 1. 前端性能监控

```typescript
class PerformanceMonitor {
  private metrics: PerformanceMetrics = {
    renderTimes: [],
    interactionTimes: [],
    memoryUsage: [],
    errorCount: 0,
  };

  startRenderMeasure(name: string): void {
    performance.mark(`${name}-start`);
  }

  endRenderMeasure(name: string): number {
    performance.mark(`${name}-end`);
    performance.measure(name, `${name}-start`, `${name}-end`);

    const measure = performance.getEntriesByName(name)[0];
    const duration = measure.duration;

    this.metrics.renderTimes.push({
      name,
      duration,
      timestamp: Date.now(),
    });

    // 清理性能标记
    performance.clearMarks(`${name}-start`);
    performance.clearMarks(`${name}-end`);
    performance.clearMeasures(name);

    return duration;
  }

  measureInteraction(name: string, fn: () => void): void {
    const start = performance.now();
    fn();
    const duration = performance.now() - start;

    this.metrics.interactionTimes.push({
      name,
      duration,
      timestamp: Date.now(),
    });

    // 如果交互时间超过 100ms，记录警告
    if (duration > 100) {
      console.warn(`Slow interaction: ${name} took ${duration.toFixed(2)}ms`);
    }
  }

  getMetrics(): PerformanceMetrics {
    return {
      ...this.metrics,
      memoryUsage: this.getMemoryUsage(),
    };
  }

  private getMemoryUsage(): MemoryInfo[] {
    if ("memory" in performance) {
      const memory = (performance as any).memory;
      return [
        {
          used: memory.usedJSHeapSize,
          total: memory.totalJSHeapSize,
          limit: memory.jsHeapSizeLimit,
          timestamp: Date.now(),
        },
      ];
    }
    return [];
  }
}

// 使用示例
const monitor = new PerformanceMonitor();

// 监控组件渲染
const MyComponent = observer(() => {
  useEffect(() => {
    monitor.startRenderMeasure("MyComponent");
    return () => {
      monitor.endRenderMeasure("MyComponent");
    };
  });

  const handleClick = () => {
    monitor.measureInteraction("button-click", () => {
      // 处理点击事件
    });
  };

  return <button onClick={handleClick}>Click me</button>;
});
```

### 2. 后端性能监控

```typescript
class ServerPerformanceMonitor {
  private metrics: ServerMetrics = {
    requestTimes: new Map(),
    memoryUsage: [],
    cpuUsage: [],
    activeConnections: 0,
  };

  middleware() {
    return (req: Request, res: Response, next: NextFunction) => {
      const start = process.hrtime.bigint();

      res.on("finish", () => {
        const duration = Number(process.hrtime.bigint() - start) / 1000000; // 转换为毫秒

        const route = `${req.method} ${req.route?.path || req.path}`;
        if (!this.metrics.requestTimes.has(route)) {
          this.metrics.requestTimes.set(route, []);
        }

        this.metrics.requestTimes.get(route)!.push({
          duration,
          statusCode: res.statusCode,
          timestamp: Date.now(),
        });

        // 记录慢请求
        if (duration > 1000) {
          console.warn(`Slow request: ${route} took ${duration.toFixed(2)}ms`);
        }
      });

      next();
    };
  }

  startSystemMonitoring(): void {
    setInterval(() => {
      this.collectSystemMetrics();
    }, 10000); // 每 10 秒收集一次
  }

  private collectSystemMetrics(): void {
    const memUsage = process.memoryUsage();
    this.metrics.memoryUsage.push({
      heapUsed: memUsage.heapUsed,
      heapTotal: memUsage.heapTotal,
      external: memUsage.external,
      timestamp: Date.now(),
    });

    // 保持最近 100 个数据点
    if (this.metrics.memoryUsage.length > 100) {
      this.metrics.memoryUsage.shift();
    }
  }
}
```

## 🧪 性能测试

### 1. 前端性能测试

```typescript
// 时间轴性能测试
describe("Timeline Performance", () => {
  test("should render 100 elements within 2 seconds", async () => {
    const elements = generateTestElements(100);
    const start = performance.now();

    render(<Timeline elements={elements} />);

    await waitFor(() => {
      expect(screen.getAllByTestId("timeline-element")).toHaveLength(100);
    });

    const duration = performance.now() - start;
    expect(duration).toBeLessThan(2000);
  });

  test("should handle element updates without lag", async () => {
    const { rerender } = render(<Timeline elements={initialElements} />);

    const updateTimes: number[] = [];

    for (let i = 0; i < 10; i++) {
      const start = performance.now();
      const updatedElements = updateElementPositions(initialElements);
      rerender(<Timeline elements={updatedElements} />);
      const duration = performance.now() - start;
      updateTimes.push(duration);
    }

    const averageTime =
      updateTimes.reduce((a, b) => a + b) / updateTimes.length;
    expect(averageTime).toBeLessThan(100); // 平均更新时间小于 100ms
  });
});

// 内存泄漏测试
describe("Memory Leak Tests", () => {
  test("should not leak memory when adding/removing elements", async () => {
    const initialMemory = getMemoryUsage();

    for (let i = 0; i < 50; i++) {
      // 添加元素
      store.addElement(createTestElement());
      await new Promise((resolve) => setTimeout(resolve, 10));

      // 删除元素
      store.removeElement(store.elements[0].id);
      await new Promise((resolve) => setTimeout(resolve, 10));
    }

    // 强制垃圾回收
    if (global.gc) {
      global.gc();
    }

    const finalMemory = getMemoryUsage();
    const memoryIncrease = finalMemory - initialMemory;

    // 内存增长应该小于 10MB
    expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024);
  });
});
```

### 2. 后端性能测试

```typescript
// 视频处理性能测试
describe("Video Processing Performance", () => {
  test("should process simple video within time limit", async () => {
    const canvasState = createSimpleCanvasState();
    const start = Date.now();

    const result = await videoProcessor.process(canvasState);

    const duration = Date.now() - start;
    expect(duration).toBeLessThan(30000); // 30 秒内完成
    expect(result).toBeDefined();
  });

  test("should handle concurrent processing", async () => {
    const tasks = Array.from({ length: 5 }, () =>
      videoProcessor.process(createTestCanvasState())
    );

    const start = Date.now();
    const results = await Promise.all(tasks);
    const duration = Date.now() - start;

    // 并发处理不应该比串行处理慢太多
    expect(duration).toBeLessThan(60000); // 1 分钟内完成
    expect(results).toHaveLength(5);
    results.forEach((result) => expect(result).toBeDefined());
  });
});

// 负载测试
describe("Load Testing", () => {
  test("should handle high request volume", async () => {
    const requests = Array.from({ length: 100 }, () =>
      request(app).post("/api/generateVideo").send(createTestCanvasState())
    );

    const start = Date.now();
    const responses = await Promise.allSettled(requests);
    const duration = Date.now() - start;

    const successCount = responses.filter(
      (r) => r.status === "fulfilled" && r.value.status < 400
    ).length;

    // 至少 90% 的请求应该成功
    expect(successCount / requests.length).toBeGreaterThan(0.9);

    // 平均响应时间应该合理
    expect(duration / requests.length).toBeLessThan(1000);
  });
});
```

## 📈 优化实施计划

### 阶段 1: 基础优化 (1-2 周)

1. **Store 分解**

   - 拆分单体 Store 类
   - 实现细粒度状态管理
   - 优化状态更新机制

2. **组件优化**

   - 添加 React.memo 和 useMemo
   - 实现组件级错误边界
   - 优化事件处理器

3. **基础监控**

   - 实现性能监控系统
   - 添加错误追踪
   - 设置基础指标收集

4. **✅ 图片加载优化** (已完成)
   - 简化图片渲染逻辑，移除复杂的 Intersection Observer
   - 使用浏览器原生 `loading="lazy"` 属性实现懒加载
   - 添加响应式图片支持 (srcSet)
   - 优化缩略图质量和尺寸配置
   - 改进骨架屏加载体验

### 阶段 2: 深度优化 (2-3 周)

1. **时间轴虚拟化**

   - 实现虚拟滚动
   - 优化大量元素渲染
   - 改进交互性能

2. **媒体处理优化**

   - 实现缩略图缓存
   - 优化音频波形生成
   - 改进媒体加载策略

3. **后端优化**
   - 实现硬件加速检测
   - 优化 FFmpeg 命令生成
   - 改进资源管理

### 阶段 3: 高级优化 (2-3 周)

1. **缓存系统**

   - 实现多层缓存
   - 优化数据存储
   - 改进缓存策略

2. **并发处理**

   - 实现任务队列
   - 优化并发控制
   - 改进错误处理

3. **性能测试**
   - 建立性能测试套件
   - 实现自动化测试
   - 设置性能回归检测

### 阶段 4: 监控和维护 (持续)

1. **监控系统**

   - 实时性能监控
   - 用户体验追踪
   - 系统健康检查

2. **持续优化**
   - 定期性能审查
   - 用户反馈收集
   - 优化策略调整

## 🔧 开发工具和配置

### 1. 性能分析工具

```json
{
  "scripts": {
    "analyze": "npm run build && npx webpack-bundle-analyzer build/static/js/*.js",
    "profile": "npm start -- --profile",
    "perf-test": "jest --testPathPattern=performance",
    "memory-test": "node --expose-gc --max-old-space-size=4096 scripts/memory-test.js"
  }
}
```

### 2. 监控配置

```typescript
// 生产环境监控配置
const monitoringConfig = {
  performance: {
    enabled: process.env.NODE_ENV === "production",
    sampleRate: 0.1, // 10% 采样率
    thresholds: {
      renderTime: 100,
      interactionTime: 50,
      memoryUsage: 500 * 1024 * 1024, // 500MB
    },
  },

  logging: {
    level: process.env.LOG_LEVEL || "info",
    enableConsole: process.env.NODE_ENV !== "production",
    enableFile: true,
    enableRemote: process.env.NODE_ENV === "production",
  },
};
```

## 📚 最佳实践

### 1. 代码优化

- 使用 TypeScript 严格模式
- 实现适当的错误边界
- 避免不必要的重渲染
- 合理使用缓存策略

### 2. 资源管理

- 及时清理不使用的资源
- 实现资源池化
- 监控内存使用情况
- 优化网络请求

### 3. 用户体验

- 提供加载状态指示
- 实现渐进式加载
- 优化首屏加载时间
- 确保交互响应性

### 4. 监控和调试

- 建立完善的监控体系
- 实现性能指标收集
- 设置告警机制
- 定期进行性能审查

通过实施这些优化策略，Fabric 视频编辑器将能够提供更流畅、更稳定的用户体验，同时具备更好的可扩展性和维护性。
